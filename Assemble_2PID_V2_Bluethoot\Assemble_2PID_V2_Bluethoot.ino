#include <QTRSensors.h>
#include <SerialBT.h>

//SerialBT SerialBT;

QTRSensors qtr;
const uint8_t SensorCount = 8;
uint16_t sensorValues[SensorCount];
 
const int BUFFER_SIZE = 64;
char buffer[BUFFER_SIZE];
int bufferIndex = 0;

// PID constants for straight and curve
float Kp = 0.15, Ki = 0.0, Kd = 0.095;    //((Para 1000 rpm-> Reta   Kp = 0.15, Ki = 0.0, Kd = 0.95; )   
float Kp_c = 0.15, Ki_c = 0.0, Kd_c = 0.095;  //((Para 3000 rpm-> Reta   Kp = 0.3, Ki = 0.0, Kd = 0.15; vEL=35) 

// PID variables
int P, D, I, previousError = 0, PIDvalue, error;
int lsp, rsp;
int lfspeed = 150;    // Speed for straight
int lfspeed_c = 150;  // Speed for curve

// Motor pins
const uint8_t EN_A = 15, IN1_A = 14;
const uint8_t IN1_B = 17, EN_B = 16;


// State sensors
const uint8_t Sensor1 = 26, Sensor2 = 27;

// System states
enum EstadoSistema { ESTADO_INICIAL, ESTADO_RETA, ESTADO_CURVA, ESTADO_PARADO, ESTADO_REGULACAO };
EstadoSistema estadoAtual = ESTADO_INICIAL;

  // Estados do sistema
  enum EstadoTempo { TEMPO_INICIAL, TEMPO_1,TEMPO_2, TEMPO_3, TEMPO_4, TEMPO_FINAL }; 
EstadoTempo TempoAtual = TEMPO_INICIAL;
unsigned long tempoInicioEstado = 0;


// Transition control
bool condicao1Anterior = false, condicao2Anterior = false;
int contadorTransicoes = 0;
const int TRANSICOES_PARA_MUDANCA = 2;

// Permission control
bool permissaoParaIniciar = false;
int Tempo_Trajeto = 0;
// Sensor thresholds and limits
const int SENSOR_THRESHOLD = 500;
const int INTEGRAL_LIMIT = 10000;
const int MAX_MOTOR_SPEED = 250;

// Bluetooth timeout variables
unsigned long lastBTActivity = 0;
const unsigned long BT_TIMEOUT = 5000; // 5 seconds

// Debug mode flag
bool debugMode = false;

// Add these variables to the global variables section
bool stateChangeRequested = false;
EstadoSistema requestedState = ESTADO_INICIAL;

void setup() {
  Serial.begin(115200); // Inicializa a comunicação serial primeiro
  SerialBT.begin();     // Inicializa o Bluetooth


//Marcações de tempo
tempoInicioEstado = millis();
  
  Serial.println("Bluetooth iniciado!");
  
  // QTR sensor calibration
  qtr.setTypeRC();
  qtr.setSensorPins((const uint8_t[]){1, 2, 3, 4, 5, 6, 7, 8}, SensorCount);
  qtr.setEmitterPin(0);

  pinMode(LED_BUILTIN, OUTPUT);
  digitalWrite(LED_BUILTIN, HIGH); // Turn on LED during calibration
  



  // Motor configuration
  pinMode(IN1_A, OUTPUT);
  pinMode(IN1_B, OUTPUT);
  pinMode(EN_A, OUTPUT);
  pinMode(EN_B, OUTPUT);

  // Ensure motors are stopped at startup
  analogWrite(EN_A, 0);
  analogWrite(EN_B, 0);
  analogWrite(IN1_A, 0);
  analogWrite(IN1_B, 0);
 
  Serial.println("Sistema iniciado - Estado Inicial");
  Serial.println("Aguardando comando 'Start' ou 'Calibrate'...");

  digitalWrite(LED_BUILTIN, HIGH);
  Serial.println();

}

void loop() {
  maquinaEstadosTemporizada();
  processarComandosBluetooth();
   

  
  // Process pending state change requests
  if (stateChangeRequested) {
    // For safety-critical states (PARADO), apply immediately
    if (requestedState == ESTADO_PARADO) {
      transitionToState(ESTADO_PARADO);
      stateChangeRequested = false;
      Serial.println("Estado PARADO aplicado imediatamente por segurança");
      SerialBT.println("Estado PARADO aplicado imediatamente por segurança");
    } else {
      // For other states, wait for confirmation
      //Serial.println("Aguardando confirmação para mudança de estado");
      //SerialBT.println("Aguardando confirmação para mudança de estado");
    }
  }
  
  uint16_t position = qtr.readLineWhite(sensorValues);
  error = 3500 - position;

  int Valor_Sensor1 = analogRead(Sensor1);
  int Valor_Sensor2 = analogRead(Sensor2);

    Serial.print("Sensor1: "); Serial.print(Valor_Sensor1);
    Serial.print(" Sensor2: "); Serial.print(Valor_Sensor2);
    Serial.print(" Estado: "); 
    switch(estadoAtual) {
      case ESTADO_INICIAL: Serial.println("INICIAL"); break;
      case ESTADO_RETA: Serial.println("RETA"); break;
      case ESTADO_CURVA: Serial.println("CURVA"); break;
      case ESTADO_PARADO: Serial.println("PARADO"); break;
      case ESTADO_REGULACAO: Serial.println("REGULACAO"); break;
   
  }

  // Check if Sensor2 indicates stop condition
  if (Valor_Sensor2 < SENSOR_THRESHOLD && estadoAtual != ESTADO_PARADO && estadoAtual != ESTADO_INICIAL  && Valor_Sensor1 > SENSOR_THRESHOLD) {
    estadoAtual = ESTADO_PARADO;
    Serial.println("Parando: Sensor2 < 500");
  }

  if (permissaoParaIniciar || estadoAtual != ESTADO_INICIAL) {
    gerenciarEstados(Valor_Sensor1, Valor_Sensor2);
    executarControlePID();
  }
}

void processarComandosBluetooth() {
  while (SerialBT.available()) {
    lastBTActivity = millis();
    char c = SerialBT.read();
    
    // Store in buffer until newline or buffer full
    if (c != '\n' && c != '\r' && bufferIndex < BUFFER_SIZE-1) {
      buffer[bufferIndex++] = c;
    } else if (bufferIndex > 0) { // Only process if buffer has content
      buffer[bufferIndex] = '\0'; // Terminate string
      
      // Echo command back to BT client
      SerialBT.print("Recebido: ");
      SerialBT.println(buffer);
      
      // Process control commands first for better responsiveness
      if (strcmp(buffer, "Stop") == 0) {
        requestedState = ESTADO_PARADO;
        stateChangeRequested = true;
        Serial.println("Comando: Parar solicitado");
        SerialBT.println("Comando: Parar solicitado");
      } else if (strcmp(buffer, "Start") == 0) {
        permissaoParaIniciar = true;
        // If already stopped, transition to RETA state
        if (estadoAtual == ESTADO_PARADO || estadoAtual == ESTADO_INICIAL) {
          requestedState = ESTADO_RETA;
          stateChangeRequested = true;
          transitionToState(ESTADO_RETA);
          Serial.println("Solicitando mudança para Estado Reta");
          SerialBT.println("Solicitando mudança para Estado Reta");
        }
      } else if (strcmp(buffer, "regulacao") == 0) {
        requestedState = ESTADO_REGULACAO;
        stateChangeRequested = true;
        Serial.println("Comando: Regulação solicitado");
        SerialBT.println("Comando: Regulação solicitado");
      } else if (strcmp(buffer, "Calibrate") == 0) {
        // Request calibration state
        EstadoSistema estadoAnterior = estadoAtual;
        requestedState = ESTADO_PARADO;
        stateChangeRequested = true;
        Serial.println("Calibração solicitada");
        SerialBT.println("Calibração solicitada");
        
        // Perform calibration after state change is confirmed
        if (estadoAtual == ESTADO_PARADO) {
          calibrarSensores();
          // After calibration, request to return to previous state if it wasn't INICIAL
          if (estadoAnterior != ESTADO_INICIAL) {
            requestedState = estadoAnterior;
            stateChangeRequested = true;
          } else {
            Serial.println("Calibração concluída. Aguardando comando 'Start'...");
            SerialBT.println("Calibração concluída. Aguardando comando 'Start'...");
          }
        }
      } 
      // Add a new command to confirm state changes
      else if (strcmp(buffer, "ConfirmStateChange") == 0) {
        if (stateChangeRequested) {
          transitionToState(requestedState);
          stateChangeRequested = false;
          Serial.println("Mudança de estado confirmada");
          SerialBT.println("Mudança de estado confirmada");
        } else {
          Serial.println("Nenhuma mudança de estado pendente");
          SerialBT.println("Nenhuma mudança de estado pendente");
        }
      }/*
      // Process PID constants for straight
      else if (strncmp(buffer, "PR=", 3) == 0) {
        Kp = atof(buffer + 3);
        Serial.print("Kp Reta: "); Serial.println(Kp);
        SerialBT.print("Kp Reta: "); SerialBT.println(Kp);
      } else if (strncmp(buffer, "IR=", 3) == 0) {
        Ki = atof(buffer + 3);
        Serial.print("Ki Reta: "); Serial.println(Ki);
        SerialBT.print("Ki Reta: "); SerialBT.println(Ki);
      } else if (strncmp(buffer, "DR=", 3) == 0) {
        Kd = atof(buffer + 3);
        Serial.print("Kd Reta: "); Serial.println(Kd);
        SerialBT.print("Kd Reta: "); SerialBT.println(Kd);
      } 
      // Process PID constants for curve
      else if (strncmp(buffer, "PC=", 3) == 0) {
        Kp_c = atof(buffer + 3);
        Serial.print("Kp Curva: "); Serial.println(Kp_c);
        SerialBT.print("Kp Curva: "); SerialBT.println(Kp_c);
      } else if (strncmp(buffer, "IC=", 3) == 0) {
        Ki_c = atof(buffer + 3);
        Serial.print("Ki Curva: "); Serial.println(Ki_c);
        SerialBT.print("Ki Curva: "); SerialBT.println(Ki_c);
      } else if (strncmp(buffer, "DC=", 3) == 0) {
        Kd_c = atof(buffer + 3);
        Serial.print("Kd Curva: "); Serial.println(Kd_c);
        SerialBT.print("Kd Curva: "); SerialBT.println(Kd_c);
      }
      // Process speed commands
      else if (strncmp(buffer, "SR=", 3) == 0) {
        lfspeed = atoi(buffer + 3);
        Serial.print("Velocidade Reta: "); Serial.println(lfspeed);
        SerialBT.print("Velocidade Reta: "); SerialBT.println(lfspeed);
      } else if (strncmp(buffer, "SC=", 3) == 0) {
        lfspeed_c = atoi(buffer + 3);
        Serial.print("Velocidade Curva: "); Serial.println(lfspeed_c);
        SerialBT.print("Velocidade Curva: "); SerialBT.println(lfspeed_c);
      }*/
      // Process control commands
      else if (strcmp(buffer, "Stop") == 0) {
        estadoAtual = ESTADO_PARADO;
        Serial.println("Comando: Parar");
        SerialBT.println("Comando: Parar");
      } else if (strcmp(buffer, "regulacao") == 0) {
        estadoAtual = ESTADO_REGULACAO;
        Serial.println("Comando: Regulação");
        SerialBT.println("Comando: Regulação");
      } else if (strcmp(buffer, "Calibrate") == 0 || strcmp(buffer, "Start") == 0) {
        permissaoParaIniciar = true;
        if (estadoAtual == ESTADO_INICIAL) {
          estadoAtual = ESTADO_RETA;
        }
        Serial.println("Permissão concedida para iniciar");
        SerialBT.println("Permissão concedida para iniciar");
      } else if (strcmp(buffer, "status") == 0) {
        // Send current status back to BT client
        enviarStatus();
      } else if (strcmp(buffer, "debug") == 0) {
        debugMode = !debugMode;
        SerialBT.print("Debug mode: "); 
        SerialBT.println(debugMode ? "ON" : "OFF");
        Serial.print("Debug mode: "); 
        Serial.println(debugMode ? "ON" : "OFF");
      }
      
      // Reset buffer
      bufferIndex = 0;
    }
  }
  
  // Check for BT connection timeout
  if (millis() - lastBTActivity > BT_TIMEOUT && estadoAtual != ESTADO_PARADO && estadoAtual != ESTADO_INICIAL) {
    Serial.println("Bluetooth timeout - stopping for safety");
    requestedState = ESTADO_PARADO;
    stateChangeRequested = true;
  }
}


void maquinaEstadosTemporizada() {
  unsigned long tempoAtual = millis();
  unsigned long tempoDecorrido = (tempoAtual - tempoInicioEstado) / 1000; // Convertendo para segundos

  switch (TempoAtual) {
    case TEMPO_INICIAL:
      Serial.println("Estado INICIAL: Aguardando início do ciclo");
      if (tempoDecorrido >= 1) {
        TempoAtual = TEMPO_1;
        tempoInicioEstado = tempoAtual;
        
      }
      break;

    case TEMPO_1:
      Serial.println("Estado 1: Executando operações do tempo 1-4s");
      if (tempoDecorrido >= 4) {
        TempoAtual = TEMPO_2;
        tempoInicioEstado = tempoAtual;
      }
      break;

    case TEMPO_2:
      Serial.println("Estado 2: Executando operações do tempo 4-5s");
      if (tempoDecorrido >= 1) { // 5-4 = 1s de duração
        TempoAtual = TEMPO_3;
        tempoInicioEstado = tempoAtual;
      }
      break;

    case TEMPO_3:
      Serial.println("Estado 3: Executando operações do tempo 5-8s");
      if (tempoDecorrido >= 3) { // 8-5 = 3s de duração
        TempoAtual = TEMPO_4;
        tempoInicioEstado = tempoAtual;
      }
      break;

    case TEMPO_4:
      Serial.println("Estado 4: Executando operações do tempo 8-10s");
      if (tempoDecorrido >= 2) { // 10-8 = 2s de duração
        TempoAtual = TEMPO_FINAL;
        tempoInicioEstado = tempoAtual;
      }
      break;

    case TEMPO_FINAL:
      Serial.println("Estado FINAL: Ciclo completo. Reiniciando...");
      if (tempoDecorrido >= 2) { // Tempo no estado final
        // TempoAtual = TEMPO_INICIAL;
        tempoInicioEstado = tempoAtual;
      }
      break;
  }


void enviarStatus() {
  SerialBT.println("=== STATUS ATUAL ===");
  SerialBT.print("Estado: ");
  switch(estadoAtual) {
    case ESTADO_INICIAL: SerialBT.println("INICIAL"); break;
    case ESTADO_RETA: SerialBT.println("RETA"); break;
    case ESTADO_CURVA: SerialBT.println("CURVA"); break;
    case ESTADO_PARADO: SerialBT.println("PARADO"); break;
    case ESTADO_REGULACAO: SerialBT.println("REGULACAO"); break;
  }
  
  SerialBT.println("PID Reta:");
  SerialBT.print("  Kp="); SerialBT.println(Kp);
  SerialBT.print("  Ki="); SerialBT.println(Ki);
  SerialBT.print("  Kd="); SerialBT.println(Kd);
  
  SerialBT.println("PID Curva:");
  SerialBT.print("  Kp="); SerialBT.println(Kp_c);
  SerialBT.print("  Ki="); SerialBT.println(Ki_c);
  SerialBT.print("  Kd="); SerialBT.println(Kd_c);
  
  SerialBT.print("Velocidade Reta: "); SerialBT.println(lfspeed);
  SerialBT.print("Velocidade Curva: "); SerialBT.println(lfspeed_c);
}

void gerenciarEstados(int sensor1, int sensor2) {
  bool condicao1 = (sensor1 < SENSOR_THRESHOLD);
  bool condicao2 = (sensor1 > SENSOR_THRESHOLD);

  // Skip state management if in PARADO or REGULACAO states
  if (estadoAtual == ESTADO_PARADO || estadoAtual == ESTADO_REGULACAO) {
    return;
  }

  if (estadoAtual == ESTADO_INICIAL && condicao1 && permissaoParaIniciar) {
    estadoAtual = ESTADO_RETA;
    contadorTransicoes = 0;
    Serial.println("Mudando para Estado Reta");
    return;
  }

  if (estadoAtual != ESTADO_INICIAL) {
    // Detect transitions
    if (condicao1Anterior && !condicao1 && condicao2) {
      contadorTransicoes++;
      Serial.print("Transição detectada: "); Serial.println(contadorTransicoes);
    }
    if (condicao2Anterior && !condicao2 && condicao1) {
      contadorTransicoes++;
      Serial.print("Transição detectada: "); Serial.println(contadorTransicoes);
    }

    if (contadorTransicoes >= TRANSICOES_PARA_MUDANCA) {
      mudarEstado();
      contadorTransicoes = 0;
    }
  }

  condicao1Anterior = condicao1;
  condicao2Anterior = condicao2;
}

void mudarEstado() {
  if (estadoAtual == ESTADO_RETA) {
    estadoAtual = ESTADO_CURVA;
    Serial.println("Mudando para Estado Curva");
    SerialBT.println("Mudando para Estado Curva");
  } else {
    estadoAtual = ESTADO_RETA;
    Serial.println("Mudando para Estado Reta");
    SerialBT.println("Mudando para Estado Reta");
  }
}

void executarControlePID() {

  switch (estadoAtual) {
    case ESTADO_RETA:
      kp = Kp; ki = Ki; kd = Kd; lfspeed = lfspeed;
      break;
    case ESTADO_CURVA:
      kp = Kp_c; Ki = Ki_c; kd = Kd_c; lfspeed  = lfspeed_c;
      break;
    case ESTADO_PARADO:
      // Stop motors properly
      analogWrite(EN_A, 0);
      digitalWrite(IN1_A, LOW);
      analogWrite(EN_B, 0);
      digitalWrite(IN1_B, LOW);
      return;
    case ESTADO_REGULACAO:
    // Rotate in circle - one motor forward, one backward
    // analogWrite(EN_A, 30);
    //  analogWrite(IN1_A, 0);
    // analogWrite(IN1_B, 30);
    // analogWrite(EN_B, 0);
      return;
    case ESTADO_INICIAL:
      // Ensure motors are stopped in initial state too
      analogWrite(EN_A, 0);
      digitalWrite(IN1_A, LOW);
      analogWrite(EN_B, 0);
      digitalWrite(IN1_B, LOW);
      return;
    default:
      return;
  }

  // PID calculation
  P = error;
  I = constrain(I + error, -INTEGRAL_LIMIT, INTEGRAL_LIMIT); // Prevent integral windup
  D = error - previousError;
  
  PIDvalue = (kp * P) + (ki * I) + (kd * D);
  previousError = error;

  // Debug output if enabled
  if (debugMode) {
    Serial.print("P:"); Serial.print(P);
    Serial.print(" I:"); Serial.print(I);
    Serial.print(" D:"); Serial.print(D);
    Serial.print(" PID:"); Serial.println(PIDvalue);
    
    SerialBT.print("P:"); SerialBT.print(P);
    SerialBT.print(" I:"); SerialBT.print(I);
    SerialBT.print(" D:"); SerialBT.print(D);
    SerialBT.print(" PID:"); SerialBT.println(PIDvalue);
  }

  // Speed calculation
  lsp = lfspeed - PIDvalue;
  rsp = lfspeed + PIDvalue;

  // Speed limitation
  lsp = constrain(lsp, -MAX_MOTOR_SPEED, MAX_MOTOR_SPEED);
  rsp = constrain(rsp, -MAX_MOTOR_SPEED, MAX_MOTOR_SPEED);

    Serial.print("| lsp");
    SerialBT.print("| lsp");
    Serial.print(lsp);
    SerialBT.print(lsp);
    Serial.print("| rsp");
    SerialBT.print("| rsp");
    Serial.print(rsp);
    SerialBT.print(rsp);
    Serial.println("|");
    SerialBT.println("|");

  if(rsp < 0) {
    rsp = -rsp;  // Converte para valor positivo para PWM
    analogWrite(IN1_A,rsp);  // Inverte o sentido
    
  }  else {
    
    analogWrite(EN_A, rsp);  // Sentido normal
  }

  // Controle do sentido de rotação do motor B
  if(lsp < 0) {
    lsp = -lsp;  // Converte para valor positivo para PWM
    analogWrite(IN1_B, lsp);  // Inverte o sentido

  } else{
    analogWrite(EN_B, lsp);   // Sentido normal
  }


  // if ((error > 0 && previousError < 0) || (error < 0 && previousError > 0)) {
    // I = 0; // Reset integral when crossing the line
  // }

}




void calibrarSensores() {
  Serial.println("Calibrando sensores...");
  
  // Ensure motors are stopped during calibration
  stopMotors();
  
  // Turn on LED to indicate calibration mode
  digitalWrite(LED_BUILTIN, HIGH);
  
  // Perform calibration without motor movement
  for (uint16_t i = 0; i < 400; i++) {
    qtr.calibrate();
    if (i % 100 == 0) {
      Serial.print(".");
      SerialBT.print(".");
    }
  }
  
  // Turn off LED when calibration is complete
  digitalWrite(LED_BUILTIN, LOW);
  
  Serial.println("\nCalibração concluída!");
  SerialBT.println("\nCalibração concluída!");
}

// Add a function to safely transition between states
void transitionToState(EstadoSistema newState) {
  // Handle any cleanup needed for the current state
  if (estadoAtual == ESTADO_PARADO && newState != ESTADO_PARADO) {
    // Reset PID variables when leaving PARADO state
    I = 0;
    previousError = 0;
  }
  
  // Set the new state
  estadoAtual = newState;
  
  // Log the transition
  Serial.print("Transição de estado: ");
  SerialBT.print("Transição de estado: ");
  
  switch(newState) {
    case ESTADO_INICIAL: 
      Serial.println("INICIAL"); 
      SerialBT.println("INICIAL");
      break;
    case ESTADO_RETA: 
      Serial.println("RETA"); 
      SerialBT.println("RETA");
      break;
    case ESTADO_CURVA: 
      Serial.println("CURVA"); 
      SerialBT.println("CURVA");
      break;
    case ESTADO_PARADO: 
      Serial.println("PARADO"); 
      SerialBT.println("PARADO");
      break;
    case ESTADO_REGULACAO: 
      Serial.println("REGULACAO"); 
      SerialBT.println("REGULACAO");
      break;
  }
}


void stopMotors() {
  analogWrite(EN_A, 0);
  analogWrite(IN1_A, 0);
  analogWrite(EN_B, 0);
  analogWrite(IN1_B, 0);
}